import React, { useState, useEffect, useMemo, useCallback, KeyboardEvent, useRef, useReducer } from 'react';
import {
    Box,
    Grid,
    Paper,
    List,
    ListItem,
    ListItemText,
    Checkbox,
    TextField,
    Button,
    Typography,
    IconButton,
    ListItemIcon,
    ListItemSecondaryAction,
    Tooltip,
    InputAdornment
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import TemplateIcon from '@mui/icons-material/Description';
import { styled } from '@mui/material/styles';
import { processData } from '@/services/dataProcessingService';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { EditTemplateDialog } from '@/components/dialogs/EditTemplateDialog';
import { JobConfirmationDialog } from '@/components/dialogs/JobConfirmationDialog';
import { useGetTemplateVars } from '@/lib/hooks/useTemplateVars';
import { useRouter } from 'next/navigation';

interface Template {
    id: string;
    name: string;
    description: string;
}

interface Dataset {
    id: string;
    name: string;
    description: string;
}

interface CreateJobProps {
    templates: Template[];
    datasets: Dataset[];
}

// State-Typ für den Reducer definieren
interface JobState {
    selectedTemplate: Template | null;
    selectedDatasets: Set<string>;
    lastSelectedDataset: string | null;
    focusedDatasetId: string | null;
    selectionAnchor: string | null;
    templateFilter: string;
    datasetFilter: string;
    editingTemplateId: string | null;
    isProcessing: boolean;
    showErrorDialog: boolean;
    errorMessage: string;
    isConfirmationOpen: boolean;
}

// Aktionstypen für den Reducer definieren
type JobAction =
    | { type: 'SELECT_TEMPLATE'; template: Template }
    | { type: 'UPDATE_DATASET_SELECTION'; datasetId: string; isShiftKey: boolean; isCtrlKey: boolean; datasets: Dataset[] }
    | { type: 'SET_FOCUSED_DATASET'; datasetId: string | null }
    | { type: 'SET_TEMPLATE_FILTER'; filter: string }
    | { type: 'SET_DATASET_FILTER'; filter: string }
    | { type: 'EDIT_TEMPLATE'; templateId: string }
    | { type: 'CLOSE_TEMPLATE_EDITOR' }
    | { type: 'SELECT_ALL_DATASETS'; datasets: Dataset[] }
    | { type: 'SET_PROCESSING'; isProcessing: boolean }
    | { type: 'SHOW_ERROR'; message: string }
    | { type: 'HIDE_ERROR' }
    | { type: 'SHOW_CONFIRMATION' }
    | { type: 'HIDE_CONFIRMATION' }
    | { type: 'RESET_SELECTIONS' };

// Initialer State für den Reducer
const initialState: JobState = {
    selectedTemplate: null,
    selectedDatasets: new Set<string>(),
    lastSelectedDataset: null,
    focusedDatasetId: null,
    selectionAnchor: null,
    templateFilter: '',
    datasetFilter: '',
    editingTemplateId: null,
    isProcessing: false,
    showErrorDialog: false,
    errorMessage: '',
    isConfirmationOpen: false
};

// Reducer-Funktion implementieren
function jobReducer(state: JobState, action: JobAction): JobState {
    switch (action.type) {
        case 'SELECT_TEMPLATE':
            return {
                ...state,
                selectedTemplate: state.selectedTemplate?.id === action.template.id ? null : action.template
            };

        case 'UPDATE_DATASET_SELECTION': {
            const { datasetId, isShiftKey, isCtrlKey, datasets } = action;
            const newSelected = new Set(state.selectedDatasets);
            let newAnchor = state.selectionAnchor;

            if (isShiftKey) {
                // Falls noch kein Anker existiert, nutze das zuletzt ausgewählte Element (oder datasetId)
                if (!newAnchor) {
                    newAnchor = state.lastSelectedDataset || datasetId;
                }

                const filteredDatasets = datasets.filter(d => {
                    const searchTerm = state.datasetFilter.toLowerCase().trim();
                    if (!searchTerm) return true;
                    return d.name.toLowerCase().includes(searchTerm) ||
                          (d.description?.toLowerCase() || '').includes(searchTerm);
                });

                const anchorIndex = filteredDatasets.findIndex(d => d.id === newAnchor);
                const targetIndex = filteredDatasets.findIndex(d => d.id === datasetId);

                if (anchorIndex === -1 || targetIndex === -1) {
                    return state; // No change if indices are invalid
                }

                const startIdx = Math.min(anchorIndex, targetIndex);
                const endIdx = Math.max(anchorIndex, targetIndex);

                // Wenn nicht Ctrl gedrückt ist, vorherige Auswahl verwerfen
                if (!isCtrlKey) {
                    newSelected.clear();
                }

                // Alles im Bereich auswählen
                for (let i = startIdx; i <= endIdx; i++) {
                    newSelected.add(filteredDatasets[i].id);
                }
            } else if (isCtrlKey || true) {
                // Toggle just this item
                if (newSelected.has(datasetId)) {
                    newSelected.delete(datasetId);
                } else {
                    newSelected.add(datasetId);
                }
                newAnchor = datasetId;
            }

            return {
                ...state,
                selectedDatasets: newSelected,
                lastSelectedDataset: datasetId,
                selectionAnchor: newAnchor,
                focusedDatasetId: datasetId
            };
        }

        case 'SET_FOCUSED_DATASET':
            return {
                ...state,
                focusedDatasetId: action.datasetId
            };

        case 'SET_TEMPLATE_FILTER':
            return {
                ...state,
                templateFilter: action.filter
            };

        case 'SET_DATASET_FILTER':
            return {
                ...state,
                datasetFilter: action.filter
            };

        case 'EDIT_TEMPLATE':
            return {
                ...state,
                editingTemplateId: action.templateId
            };

        case 'CLOSE_TEMPLATE_EDITOR':
            return {
                ...state,
                editingTemplateId: null
            };

        case 'SELECT_ALL_DATASETS': {
            const newSelected = new Set(state.selectedDatasets);
            const filteredDatasets = action.datasets.filter(d => {
                const searchTerm = state.datasetFilter.toLowerCase().trim();
                if (!searchTerm) return true;
                return d.name.toLowerCase().includes(searchTerm) ||
                      (d.description?.toLowerCase() || '').includes(searchTerm);
            });

            const visibleDatasetIds = filteredDatasets.map(d => d.id);

            // Check if all visible datasets are selected
            const allVisible = visibleDatasetIds.every(id => state.selectedDatasets.has(id));

            if (allVisible) {
                // Deselect only visible datasets
                visibleDatasetIds.forEach(id => newSelected.delete(id));
            } else {
                // Select all visible datasets
                visibleDatasetIds.forEach(id => newSelected.add(id));
            }

            return {
                ...state,
                selectedDatasets: newSelected
            };
        }

        case 'SET_PROCESSING':
            return {
                ...state,
                isProcessing: action.isProcessing
            };

        case 'SHOW_ERROR':
            return {
                ...state,
                showErrorDialog: true,
                errorMessage: action.message
            };

        case 'HIDE_ERROR':
            return {
                ...state,
                showErrorDialog: false
            };

        case 'SHOW_CONFIRMATION':
            return {
                ...state,
                isConfirmationOpen: true
            };

        case 'HIDE_CONFIRMATION':
            return {
                ...state,
                isConfirmationOpen: false
            };

        case 'RESET_SELECTIONS':
            return {
                ...state,
                selectedTemplate: null,
                selectedDatasets: new Set<string>()
            };

        default:
            return state;
    }
}

const ScrollableList = styled(Paper)(({ theme }) => ({
    maxHeight: '60vh',
    overflow: 'auto',
    padding: theme.spacing(1),
    backgroundColor: theme.palette.background.default,
}));

const SearchField = styled(TextField)(({ theme }) => ({
    marginBottom: theme.spacing(2),
    width: '100%',
}));

export const CreateJob: React.FC<CreateJobProps> = ({ templates, datasets }) => {
    const router = useRouter();
    const [state, dispatch] = useReducer(jobReducer, initialState);
    const itemRefs = useRef<Record<string, HTMLLIElement | null>>({});

    // Memoized filtered lists - MUST be defined before the handlers that use them
    const filteredTemplates = useMemo(() => {
        const searchTerm = state.templateFilter.toLowerCase().trim();
        if (!searchTerm) return templates;

        return templates.filter(template =>
            template.name.toLowerCase().includes(searchTerm) ||
            (template.description?.toLowerCase() || '').includes(searchTerm)
        );
    }, [templates, state.templateFilter]);

    const filteredDatasets = useMemo(() => {
        const searchTerm = state.datasetFilter.toLowerCase().trim();
        if (!searchTerm) return datasets;

        return datasets.filter(dataset =>
            dataset.name.toLowerCase().includes(searchTerm) ||
            (dataset.description?.toLowerCase() || '').includes(searchTerm)
        );
    }, [datasets, state.datasetFilter]);

    const handleTemplateSelect = useCallback((template: Template) => {
        dispatch({ type: 'SELECT_TEMPLATE', template });
    }, []);

    const handleDatasetSelect = useCallback((datasetId: string, event: React.MouseEvent | KeyboardEvent) => {
        event.preventDefault();
        dispatch({
            type: 'UPDATE_DATASET_SELECTION',
            datasetId,
            isShiftKey: event.shiftKey,
            isCtrlKey: event.metaKey || event.ctrlKey,
            datasets
        });
    }, [datasets]);

    useEffect(() => {
        if (state.focusedDatasetId) {
            itemRefs.current[state.focusedDatasetId]?.focus();
        }
    }, [state.focusedDatasetId]);

    const handleDatasetKeyDown = useCallback((event: KeyboardEvent, datasetId: string) => {
        if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
            event.preventDefault();

            const currentIndex = filteredDatasets.findIndex(d => d.id === datasetId);
            let nextIndex = currentIndex;

            if (event.key === 'ArrowUp') {
                nextIndex = Math.max(0, currentIndex - 1);
            } else if (event.key === 'ArrowDown') {
                nextIndex = Math.min(filteredDatasets.length - 1, currentIndex + 1);
            }

            const nextDataset = filteredDatasets[nextIndex];
            if (!nextDataset) return;

            // Update dataset selection
            dispatch({
                type: 'UPDATE_DATASET_SELECTION',
                datasetId: nextDataset.id,
                isShiftKey: event.shiftKey,
                isCtrlKey: event.ctrlKey || event.metaKey,
                datasets
            });
        } else if (event.key === ' ') {
            event.preventDefault();
            dispatch({
                type: 'UPDATE_DATASET_SELECTION',
                datasetId,
                isShiftKey: event.shiftKey,
                isCtrlKey: event.ctrlKey || event.metaKey,
                datasets
            });
        }
    }, [filteredDatasets, datasets]);

    const handleSelectAllDatasets = useCallback(() => {
        dispatch({ type: 'SELECT_ALL_DATASETS', datasets });
    }, [datasets]);

    const handleEditTemplate = useCallback((templateId: string) => {
        dispatch({ type: 'EDIT_TEMPLATE', templateId });
    }, []);

    const handleProcessClick = useCallback(() => {
        dispatch({ type: 'SHOW_CONFIRMATION' });
    }, []);

    const handleConfirm = async (jobName: string) => {
        if (!state.selectedTemplate || state.selectedDatasets.size === 0) return;

        try {
            dispatch({ type: 'SET_PROCESSING', isProcessing: true });
            const result = await processData(
                [state.selectedTemplate],
                Array.from(state.selectedDatasets).map(id => ({ id, name: datasets.find(d => d.id === id)?.name || id })),
                jobName || undefined
            );
            if (result?.job?.id) {
                // Weiterleitung zur Job-Detailseite
                router.push(`/jobs/${result.job.id}`);
            } else {
                throw new Error('No job ID received from server');
            }
        } catch (error) {
            console.error('Error processing data:', error);
            dispatch({ type: 'SHOW_ERROR', message: error instanceof Error ? error.message : 'Failed to process data' });
        } finally {
            dispatch({ type: 'SET_PROCESSING', isProcessing: false });
            dispatch({ type: 'HIDE_CONFIRMATION' });
        }
    };

    const isProcessButtonEnabled = state.selectedTemplate && state.selectedDatasets.size > 0;

    useEffect(() => {
        // Reset selections when templates or datasets change
        dispatch({ type: 'RESET_SELECTIONS' });
    }, [templates, datasets]);

    return (
        <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            height: 'calc(100vh - 64px - 24px)', // Höhe minus Topbar und Bottom-Margin
            overflow: 'hidden',
            mb: 3, // Margin unten für visuellen Abstand
        }}>
            <Box sx={{
                maxWidth: '1200px',
                width: '100%',
                margin: '0 auto',
                px: 3,
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
                minHeight: 0, // Wichtig für Flex-Shrink
            }}>
                <Box sx={{
                    flex: 1,
                    minHeight: 0,
                    overflow: 'hidden',
                    mb: 2,
                }}>
                    <Grid container spacing={2} sx={{
                        height: '100%',
                        margin: 0,
                        width: '100%',
                    }}>
                        <Grid item xs={6} sx={{
                            height: '100%',
                            padding: '8px !important',
                        }}>
                            <Paper sx={{
                                height: '100%',
                                p: 2,
                                display: 'flex',
                                flexDirection: 'column',
                                overflow: 'hidden',
                            }}>
                                <Typography variant="h6" sx={{ mb: 2 }}>
                                    Job Templates
                                </Typography>
                                <TextField
                                    fullWidth
                                    size="small"
                                    placeholder="Search templates..."
                                    value={state.templateFilter}
                                    onChange={(e) => dispatch({ type: 'SET_TEMPLATE_FILTER', filter: e.target.value })}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <SearchIcon />
                                            </InputAdornment>
                                        ),
                                    }}
                                    sx={{ mb: 2 }}
                                />
                                <Box sx={{
                                    flex: 1,
                                    minHeight: 0,
                                    overflow: 'hidden',
                                }}>
                                    <List sx={{
                                        height: '100%',
                                        overflow: 'auto',
                                        '& .MuiListItem-root': {
                                            height: '32px',
                                            borderRadius: 1,
                                            mb: 0.5,
                                            px: 1.5,
                                        },
                                    }}>
                                        {filteredTemplates.map((template) => (
                                            <ListItem
                                                key={template.id}
                                                selected={state.selectedTemplate?.id === template.id}
                                                onClick={() => handleTemplateSelect(template)}
                                                sx={{
                                                    cursor: 'pointer',
                                                    userSelect: 'none',
                                                    '&:hover': {
                                                        bgcolor: 'action.hover',
                                                    },
                                                    '&.Mui-selected': {
                                                        bgcolor: 'secondary.light',
                                                        borderLeft: '4px solid',
                                                        borderLeftColor: 'secondary.main',
                                                        fontWeight: 'bold',
                                                        boxShadow: '0 2px 4px rgba(0,0,0,0.15), 0 1px 2px rgba(0,0,0,0.2)',
                                                        transform: 'translateX(2px)',
                                                        transition: 'all 0.2s ease-in-out',
                                                        '&:hover': {
                                                            bgcolor: 'secondary.light',
                                                        },
                                                        '& .MuiTypography-root': {
                                                            color: 'secondary.dark',
                                                            fontWeight: 'bold',
                                                        },
                                                        '& .MuiListItemIcon-root': {
                                                            color: 'secondary.main',
                                                        },
                                                    },
                                                }}
                                            >
                                                <ListItemIcon sx={{
                                                    minWidth: '32px',
                                                    color: 'inherit'
                                                }}>
                                                    <TemplateIcon sx={{ fontSize: '1.2rem' }} />
                                                </ListItemIcon>
                                                <Box sx={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    width: 'calc(100% - 80px)',
                                                    overflow: 'hidden',
                                                }}>
                                                    <Tooltip title={template.description || ''} arrow>
                                                        <Typography
                                                            variant="body2"
                                                            noWrap
                                                            sx={{
                                                                flex: 1,
                                                                overflow: 'hidden',
                                                                textOverflow: 'ellipsis',
                                                            }}
                                                        >
                                                            {template.name}
                                                        </Typography>
                                                    </Tooltip>
                                                </Box>
                                                <IconButton
                                                    edge="end"
                                                    size="small"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        dispatch({ type: 'EDIT_TEMPLATE', templateId: template.id });
                                                    }}
                                                >
                                                    <EditIcon sx={{ fontSize: '1.1rem' }} />
                                                </IconButton>
                                            </ListItem>
                                        ))}
                                    </List>
                                </Box>
                            </Paper>
                        </Grid>

                        <Grid item xs={6} sx={{
                            height: '100%',
                            padding: '8px !important',
                        }}>
                            <Paper sx={{
                                height: '100%',
                                p: 2,
                                display: 'flex',
                                flexDirection: 'column',
                                overflow: 'hidden',
                            }}>
                                <Typography variant="h6" sx={{ mb: 2 }}>
                                    Datasets
                                </Typography>
                                <TextField
                                    fullWidth
                                    size="small"
                                    placeholder="Search datasets..."
                                    value={state.datasetFilter}
                                    onChange={(e) => dispatch({ type: 'SET_DATASET_FILTER', filter: e.target.value })}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <SearchIcon />
                                            </InputAdornment>
                                        ),
                                    }}
                                    sx={{ mb: 2 }}
                                />
                                <Box sx={{
                                    flex: 1,
                                    minHeight: 0,
                                    overflow: 'hidden',
                                }}>
                                    <List sx={{
                                        height: '100%',
                                        overflow: 'auto',
                                        '& .MuiListItem-root': {
                                            height: '32px',
                                            borderRadius: 1,
                                            mb: 0.5,
                                            px: 1.5,
                                        },
                                    }}>
                                        <ListItem
                                            sx={{
                                                cursor: 'pointer',
                                                userSelect: 'none',
                                                borderBottom: 1,
                                                borderColor: 'grey.300',
                                                '&:hover': {
                                                    bgcolor: 'action.hover',
                                                },
                                            }}
                                        >
                                            <ListItemIcon sx={{ minWidth: '32px' }}>
                                                <Checkbox
                                                    edge="start"
                                                    checked={filteredDatasets.length > 0 &&
                                                        filteredDatasets.every(d => state.selectedDatasets.has(d.id))}
                                                    indeterminate={filteredDatasets.some(d => state.selectedDatasets.has(d.id)) &&
                                                        !filteredDatasets.every(d => state.selectedDatasets.has(d.id))}
                                                    onChange={handleSelectAllDatasets}
                                                    size="small"
                                                />
                                            </ListItemIcon>
                                            <Typography variant="body2">
                                                Select All Visible
                                            </Typography>
                                        </ListItem>
                                        {filteredDatasets.map((dataset) => (
                                            <ListItem
                                                ref={(el) => {
                                                    itemRefs.current[dataset.id] = el;
                                                }}
                                                key={dataset.id}
                                                tabIndex={state.focusedDatasetId === dataset.id ? 0 : -1}
                                                selected={state.selectedDatasets.has(dataset.id)}
                                                onClick={(e) => handleDatasetSelect(dataset.id, e)}
                                                onKeyDown={(e) => handleDatasetKeyDown(e, dataset.id)}
                                                sx={{
                                                    cursor: 'pointer',
                                                    userSelect: 'none',
                                                    '&:hover': {
                                                        bgcolor: 'action.hover',
                                                    },
                                                    '&:focus': {
                                                        bgcolor: 'action.selected',
                                                        outline: 'none',
                                                    },
                                                    '&.Mui-selected': {
                                                        bgcolor: 'secondary.light',
                                                        borderLeft: '4px solid',
                                                        borderLeftColor: 'secondary.main',
                                                        '&:hover': {
                                                            bgcolor: 'secondary.light',
                                                        },
                                                        '& .MuiTypography-root': {
                                                            color: 'secondary.dark',
                                                            fontWeight: 'bold',
                                                        },
                                                        '& .MuiCheckbox-root': {
                                                            color: 'secondary.main',
                                                        },
                                                    },
                                                }}
                                            >
                                                <ListItemIcon sx={{
                                                    minWidth: '32px',
                                                    color: 'inherit'
                                                }}>
                                                    <Checkbox
                                                        edge="start"
                                                        checked={state.selectedDatasets.has(dataset.id)}
                                                        tabIndex={-1}
                                                        size="small"
                                                    />
                                                </ListItemIcon>
                                                <Box sx={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    width: 'calc(100% - 32px)',
                                                    overflow: 'hidden',
                                                }}>
                                                    <Tooltip title={dataset.description || ''} arrow>
                                                        <Typography
                                                            variant="body2"
                                                            noWrap
                                                            sx={{
                                                                flex: 1,
                                                                overflow: 'hidden',
                                                                textOverflow: 'ellipsis',
                                                            }}
                                                        >
                                                            {dataset.name}
                                                        </Typography>
                                                    </Tooltip>
                                                </Box>
                                            </ListItem>
                                        ))}
                                    </List>
                                </Box>
                            </Paper>
                        </Grid>
                    </Grid>
                </Box>

                <Box sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    pt: 0,
                    pb: 3,
                }}>
                    <Button
                        variant="contained"
                        color="primary"
                        disabled={!isProcessButtonEnabled || state.isProcessing}
                        onClick={handleProcessClick}
                        size="large"
                    >
                        {state.isProcessing ? 'Processing...' : 'Process Data'}
                    </Button>
                </Box>
            </Box>

            <JobConfirmationDialog
                open={state.isConfirmationOpen}
                onClose={() => dispatch({ type: 'HIDE_CONFIRMATION' })}
                onConfirm={handleConfirm}
                template={state.selectedTemplate}
                datasets={datasets.filter(d => state.selectedDatasets.has(d.id))}
            />

            <ErrorDialog
                open={state.showErrorDialog}
                onClose={() => dispatch({ type: 'HIDE_ERROR' })}
                error={state.errorMessage}
                title="Process Data Error"
            />

            {state.editingTemplateId && <EditTemplateDialog
                open={true}
                onClose={() => dispatch({ type: 'CLOSE_TEMPLATE_EDITOR' })}
                templateId={state.editingTemplateId}
            />}
        </Box>
    );
};

export default CreateJob;
